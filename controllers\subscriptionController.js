const stripeService = require('../services/stripeService');
const Plan = require('../models/Plan');
const User = require('../models/User');
const Usage = require('../models/Usage');
const asyncHandler = require('express-async-handler');

// Helper function to reset usage when plan changes
const resetUsageOnPlanChange = async (userId, newPlanType) => {
    const now = new Date();

    // Remove all existing usage records for this user
    await Usage.deleteMany({ userId });

    // Create new usage record based on plan type
    let usageData = {
        userId,
        summaryCount: 0,
        planType: newPlanType,
        lastSummaryDate: null
    };

    if (newPlanType === 'free') {
        // Free users: monthly billing cycle
        const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

        usageData.billingCycleStart = cycleStart;
        usageData.billingCycleEnd = cycleEnd;
        usageData.month = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
        usageData.year = now.getFullYear();
    } else {
        // Paid users: get billing cycle from plan
        const plan = await Plan.findOne({ userId, isPlanActive: true });
        // if (plan && plan.currentPeriodStart && plan.currentPeriodEnd) {
        //     usageData.billingCycleStart = plan.currentPeriodStart;
        //     usageData.billingCycleEnd = plan.currentPeriodEnd;
        // } else {
        //     // Fallback to monthly if no active plan
        //     const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
        //     const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        //     usageData.billingCycleStart = cycleStart;
        //     usageData.billingCycleEnd = cycleEnd;
        // }
        usageData.billingCycleStart = plan.currentPeriodStart || null;
        usageData.billingCycleEnd =plan.currentPeriodEnd || null; 
    }

    const newUsage = new Usage(usageData);
    await newUsage.save();

    console.log(`Usage reset for user ${userId} - new plan: ${newPlanType}`);
};

// Helper function to reset usage on plan activation/renewal
const resetUsageOnPlanActivation = async (userId, planType) => {
    const plan = await Plan.findOne({ userId, isPlanActive: true });

    if (!plan || !plan.currentPeriodStart || !plan.currentPeriodEnd) {
        console.log('No active plan found for usage reset');
        return;
    }

    // Check if usage record already exists for this billing cycle
    const existingUsage = await Usage.findOne({
        userId,
        billingCycleStart: plan.currentPeriodStart,
        billingCycleEnd: plan.currentPeriodEnd
    });

    if (!existingUsage) {
        // Create new usage record for this billing cycle
        const usageData = {
            userId,
            billingCycleStart: plan.currentPeriodStart,
            billingCycleEnd: plan.currentPeriodEnd,
            summaryCount: 0,
            planType: planType,
            lastSummaryDate: null
        };

        const newUsage = new Usage(usageData);
        await newUsage.save();

        console.log(`New usage record created for user ${userId} - billing cycle: ${plan.currentPeriodStart} to ${plan.currentPeriodEnd}`);
    }
};

// Price IDs - You'll need to update these with actual Stripe price IDs after creating products
const PRICE_IDS = {
    basic: process.env.STRIPE_BASIC_PRICE_ID,
    premium: process.env.STRIPE_PREMIUM_PRICE_ID
};

// Create subscription
const createSubscription = asyncHandler(async (req, res) => {
    try {
        const { planType, successUrl, cancelUrl } = req.body;
        console.log("successUrl",successUrl);
        console.log("cancelUrl",cancelUrl);
        
        
        const userId = req.user.userId;
        console.log('User ID from token:', userId);

        // Validate plan type
        if (!['basic', 'premium'].includes(planType)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid plan type. Must be basic or premium.'
            });
        }

        // Get user
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Check if user already has an active subscription
        const activePlan = await Plan.findOne({
            userId: userId,
            isPlanActive: true,
            planType: { $ne: 'free' }
        });

        if (activePlan && activePlan.planType === planType) {
            return res.status(400).json({
                success: false,
                message: 'User already has same active subscription'
            });
        }

        let customerId;

        // Check if user already has a Stripe customer ID
        const existingCustomerPlan = await Plan.findOne({ 
            userId: userId, 
            stripeCustomerId: { $ne: null }
        });

        if (existingCustomerPlan && existingCustomerPlan.stripeCustomerId) {
            customerId = existingCustomerPlan.stripeCustomerId;
        } else {
            // Create Stripe customer
            const customer = await stripeService.createCustomer(user.email, user.name);
            customerId = customer.id;
        }

        // Get price ID for the plan
        const priceId = PRICE_IDS[planType];
        if (!priceId) {
            return res.status(400).json({
                success: false,
                message: 'Price ID not configured for this plan'
            });
        }

        // Create checkout session
        const session = await stripeService.createCheckoutSession(
            customerId,
            priceId,
            successUrl || `${process.env.FRONTEND_URL}/subscription/success`,
            cancelUrl || `${process.env.FRONTEND_URL}/subscription/cancel`
        );

        // Store pending plan info without affecting current user plan

        // Find existing plan
        const existingPlan = await Plan.findOne({ userId: userId });

        if (existingPlan) {
            // Update existing plan with pending info only - DON'T change current plan status
            await Plan.findByIdAndUpdate(existingPlan._id, {
                stripeCustomerId: customerId,
                stripePriceId: priceId,
                // Store pending plan type for webhook processing
                pendingPlanType: planType,
                // DON'T change isPlanActive, planType, or subscriptionStatus
                // Keep current plan active until payment success
            });
        } else {
            // Create new plan record for first-time users
            await Plan.create({
                userId: userId,
                planType: 'free', // Start with free
                planAmount: 0,
                stripeCustomerId: customerId,
                stripePriceId: priceId,
                isPlanActive: false,
                subscriptionStatus: 'incomplete',
                pendingPlanType: planType
            });
        }

        console.log(`Checkout session created for user ${userId}, pending plan: ${planType}`);

        res.status(200).json({
            success: true,
            message: 'Checkout session created successfully',
            data: {
                sessionId: session.id,
                url: session.url
            }
        });

    } catch (error) {
        console.error('Create subscription error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create subscription',
            error: error.message
        });
    }
});

// Cancel subscription
const cancelSubscription = asyncHandler(async (req, res) => {
    try {
        const userId = req.user.userId;
        const immediate = true;

        // Find user's active plan
        const plan = await Plan.findOne({ 
            userId: userId, 
            isPlanActive: true,
            stripeSubscriptionId: { $ne: null }
        });

        if (!plan) {
            return res.status(404).json({
                success: false,
                message: 'No active subscription found'
            });
        }

        let canceledSubscription;
        if (immediate) {
            canceledSubscription = await stripeService.cancelSubscriptionImmediately(plan.stripeSubscriptionId);
        } else {
            canceledSubscription = await stripeService.cancelSubscription(plan.stripeSubscriptionId);
        }

        // Update plan status
        if (immediate) {
            await Plan.findByIdAndUpdate(plan._id, {
                isPlanActive: false,
                subscriptionStatus: 'canceled',
                planExpires: new Date()
            });

            // Update user's planType to free in User table
            await User.findByIdAndUpdate(plan.userId, {
                planType: 'free',
                isPlan: true
            });

            // Reset usage to free plan limits
            if(plan.planType !== 'free'){
            await resetUsageOnPlanChange(plan.userId, 'free');
        }
        } else {
            const updateData = {
                subscriptionStatus: 'canceled'
            };

            // Only add expiry date if it exists and is valid
            if (canceledSubscription.current_period_end) {
                const endDate = new Date(canceledSubscription.current_period_end * 1000);
                if (!isNaN(endDate.getTime())) {
                    updateData.planExpires = endDate;
                }
            }

            await Plan.findByIdAndUpdate(plan._id, updateData);

            // Update user's planType to free in User table
            await User.findByIdAndUpdate(plan.userId, {
                planType: 'free',
                isPlan: false
            });

            // Reset usage to free plan limits
            await resetUsageOnPlanChange(plan.userId, 'free');
        }

        res.status(200).json({
            success: true,
            message: immediate ? 'Subscription canceled successfully' : 'Subscription will cancel at period end',
            data: {
                subscriptionId: canceledSubscription.id,
                status: canceledSubscription.status,
                cancelAtPeriodEnd: canceledSubscription.cancel_at_period_end,
                currentPeriodEnd: canceledSubscription.current_period_end
            }
        });

    } catch (error) {
        console.error('Cancel subscription error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to cancel subscription',
            error: error.message
        });
    }
});

// Get subscription status
const getSubscriptionStatus = asyncHandler(async (req, res) => {
    try {
        const userId = req.user.userId;

        const plan = await Plan.findOne({ userId: userId });

        if (!plan) {
            return res.status(200).json({
                success: true,
                data: {
                    planType: 'free',
                    isPlanActive: true,
                    subscriptionStatus: null
                }
            });
        }

        res.status(200).json({
            success: true,
            data: {
                planType: plan.planType,
                planAmount: plan.planAmount,
                isPlanActive: plan.isPlanActive,
                subscriptionStatus: plan.subscriptionStatus,
                planStartDate: plan.planStartDate,
                planExpires: plan.planExpires,
                currentPeriodStart: plan.currentPeriodStart,
                currentPeriodEnd: plan.currentPeriodEnd
            }
        });

    } catch (error) {
        console.error('Get subscription status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get subscription status',
            error: error.message
        });
    }
});

// Stripe webhook handler
const handleWebhook = asyncHandler(async (req, res) => {
    try {
        const signature = req.headers['stripe-signature'];
        const payload = req.body; // This should be raw buffer from express.raw()

        console.log('Webhook received, payload type:', typeof payload);
        console.log('Payload is Buffer:', Buffer.isBuffer(payload));

        // Verify webhook signature
        const event = stripeService.verifyWebhookSignature(payload, signature);

        console.log('Webhook event type:', event.type);

        switch (event.type) {
            case 'checkout.session.completed':
                await handleCheckoutSessionCompleted(event.data.object);
                break;

            case 'invoice.payment_succeeded':
                await handleInvoicePaymentSucceeded(event.data.object);
                break;

            case 'invoice.payment_failed':
                await handleInvoicePaymentFailed(event.data.object);
                break;

            case 'customer.subscription.updated':
                await handleSubscriptionUpdated(event.data.object);
                break;

            case 'customer.subscription.deleted':
                await handleSubscriptionDeleted(event.data.object);
                break;

            default:
                console.log(`Unhandled event type: ${event.type}`);
        }

        res.status(200).json({ received: true });

    } catch (error) {
        console.error('Webhook error:', error);
        res.status(400).json({
            success: false,
            message: 'Webhook error',
            error: error.message
        });
    }
});

// Handle checkout session completed
const handleCheckoutSessionCompleted = async (session) => {
    try {
        const customerId = session.customer;
        const subscriptionId = session.subscription;
        console.log("naye sub id is",subscriptionId);
        

        // Find plan by customer ID
        const plan = await Plan.findOne({ stripeCustomerId: customerId });
        if (!plan) {
            console.error('Plan not found for customer:', customerId);
            return;
        }

        console.log("i am the plan ",plan);
        
        if(plan.stripeSubscriptionId && plan.subscriptionStatus=='active'){
            console.log("older sub id is",plan.stripeSubscriptionId);
            const CancelSubscription=await stripeService.cancelSubscriptionImmediately(plan.stripeSubscriptionId);
            console.log("CancelSubscription",CancelSubscription);
             

        }
        
console.log("ab bahir ah gia hn");

        // Get subscription details from Stripe
        const subscription = await stripeService.getSubscription(subscriptionId);
        const item = subscription.items.data[0];
        console.log("ma hn item ",item);
        
        const periodStart = new Date(item.current_period_start * 1000);
        const periodEnd = new Date(item.current_period_end * 1000);
        console.log("periodStart",periodStart);
        console.log("periodEnd",periodEnd);

        console.log('Subscription details:', {
            id: subscription.id,
            status: subscription.status,
            current_period_start: periodStart,
            current_period_end: periodEnd
        });

        // Use pending plan type if available, otherwise keep current plan type
        const finalPlanType = plan.pendingPlanType || plan.planType;

        // Prepare update data with proper date handling
        const updateData = {
            stripeSubscriptionId: subscriptionId,
            isPlanActive: true,
            subscriptionStatus: subscription.status,
            planType: finalPlanType, // Update to the pending plan type
            pendingPlanType: null // Clear pending plan type
        };
        updateData.planStartDate = periodStart;
        updateData.currentPeriodStart = periodStart;
        updateData.planExpires = periodEnd;
        updateData.currentPeriodEnd = periodEnd;
        

        // Only add dates if they exist and are valid
        // if (subscription.current_period_start) {
        //     const startDate = new Date(subscription.current_period_start * 1000);
        //     if (!isNaN(startDate.getTime())) {
        //         updateData.planStartDate = startDate;
        //         updateData.currentPeriodStart = startDate;
        //     }
        // }

        // if (subscription.current_period_end) {
        //     const endDate = new Date(subscription.current_period_end * 1000);
        //     if (!isNaN(endDate.getTime())) {
        //         updateData.planExpires = endDate;
        //         updateData.currentPeriodEnd = endDate;
        //     }
        // }

        // Update plan with subscription details
        await Plan.findByIdAndUpdate(plan._id, updateData);

        // Update user's planType in User table
        await User.findByIdAndUpdate(plan.userId, {
            planType: finalPlanType,
            isPlan: true
        });

        // Reset usage for new billing cycle
        await resetUsageOnPlanActivation(plan.userId, finalPlanType);

        console.log(`Plan activated: ${plan.planType} → ${finalPlanType} for user ${plan.userId}`);

        console.log('Subscription activated for customer:', customerId);
    } catch (error) {
        console.error('Error handling checkout session completed:', error);
    }
};

// Handle invoice payment succeeded
const handleInvoicePaymentSucceeded = async (invoice) => {
    try {
        const subscriptionId = invoice.subscription;

        if (!subscriptionId) return;

        // Get subscription details
        const subscription = await stripeService.getSubscription(subscriptionId);
        const item = subscription.items.data[0];

        const periodStart = new Date(item.current_period_start * 1000);
        const periodEnd = new Date(item.current_period_end * 1000);

        // Prepare update data with proper date handling
        const updateData = {
            subscriptionStatus: subscription.status,
            isPlanActive: true
        };
        updateData.planStartDate = periodStart;
        updateData.currentPeriodStart = periodStart;
        updateData.planExpires = periodEnd;
        updateData.currentPeriodEnd = periodEnd;

        // Only add dates if they exist and are valid
        // if (subscription.current_period_start) {
        //     const startDate = new Date(subscription.current_period_start * 1000);
        //     if (!isNaN(startDate.getTime())) {
        //         updateData.currentPeriodStart = startDate;
        //     }
        // }

        // if (subscription.current_period_end) {
        //     const endDate = new Date(subscription.current_period_end * 1000);
        //     if (!isNaN(endDate.getTime())) {
        //         updateData.currentPeriodEnd = endDate;
        //         updateData.planExpires = endDate;
        //     }
        // }

        // Update plan
        const updatedPlan = await Plan.findOneAndUpdate(
            { stripeSubscriptionId: subscriptionId },
            updateData,
            { new: true }
        );

        // Update user's planType in User table
        if (updatedPlan) {
            await User.findByIdAndUpdate(updatedPlan.userId, {
                planType: updatedPlan.planType,
                isPlan: true
            });

            // Reset usage for new billing cycle (if it's a renewal)
            await resetUsageOnPlanActivation(updatedPlan.userId, updatedPlan.planType);
        }

        console.log('Payment succeeded for subscription:', subscriptionId);
    } catch (error) {
        console.error('Error handling invoice payment succeeded:', error);
    }
};

// Handle invoice payment failed
const handleInvoicePaymentFailed = async (invoice) => {
    try {
        const subscriptionId = invoice.subscription;

        if (!subscriptionId) return;

        // Get subscription details to check current status
        const subscription = await stripeService.getSubscription(subscriptionId);

        console.log('Payment failed for subscription:', subscriptionId, 'Status:', subscription.status);

        // Update plan based on subscription status
        const updateData = {
            isPlanActive: false,
            subscriptionStatus: subscription.status // Could be 'past_due', 'unpaid', or 'canceled'
        };

        // If subscription is canceled, user should revert to free plan
        if (subscription.status === 'canceled') {
            updateData.planType = 'free';
            updateData.planAmount = 0;
            console.log('Subscription canceled - reverting user to free plan');
        }

        const updatedPlan = await Plan.findOneAndUpdate(
            { stripeSubscriptionId: subscriptionId },
            updateData,
            { new: true }
        );

        // Update user's planType in User table
        if (updatedPlan) {
            const newPlanType = subscription.status === 'canceled' ? 'free' : updatedPlan.planType;

            await User.findByIdAndUpdate(updatedPlan.userId, {
                planType: newPlanType,
                isPlan: subscription.status === 'active' // Only true if subscription is active
            });

            // Reset usage if plan changed to free (canceled)
            if (subscription.status === 'canceled') {
                await resetUsageOnPlanChange(updatedPlan.userId, 'free');
            }
        }

        console.log('Payment failed - plan updated with status:', subscription.status);
    } catch (error) {
        console.error('Error handling invoice payment failed:', error);
    }
};

// Handle subscription updated
const handleSubscriptionUpdated = async (subscription) => {
    try {
        const subscriptionId = subscription.id;

        // Prepare update data with proper date handling
        const updateData = {
            subscriptionStatus: subscription.status,
            isPlanActive: subscription.status === 'active'
        };

        // Only add dates if they exist and are valid
        if (subscription.current_period_start) {
            const startDate = new Date(subscription.current_period_start * 1000);
            if (!isNaN(startDate.getTime())) {
                updateData.currentPeriodStart = startDate;
            }
        }

        if (subscription.current_period_end) {
            const endDate = new Date(subscription.current_period_end * 1000);
            if (!isNaN(endDate.getTime())) {
                updateData.currentPeriodEnd = endDate;
                updateData.planExpires = endDate;
            }
        }

        const updatedPlan = await Plan.findOneAndUpdate(
            { stripeSubscriptionId: subscriptionId },
            updateData,
            { new: true }
        );

        // Update user's planType in User table
        if (updatedPlan) {
            const newPlanType = subscription.status === 'canceled' ? 'free' : updatedPlan.planType;

            await User.findByIdAndUpdate(updatedPlan.userId, {
                planType: newPlanType,
                isPlan: subscription.status === 'active'
            });

            // Reset usage if plan changed to free (canceled)
            if (subscription.status === 'canceled') {
                await resetUsageOnPlanChange(updatedPlan.userId, 'free');
            }
        }

        console.log('Subscription updated:', subscriptionId);
    } catch (error) {
        console.error('Error handling subscription updated:', error);
    }
};

// Handle subscription deleted
const handleSubscriptionDeleted = async (subscription) => {
    try {
        const subscriptionId = subscription.id;
        const plan = await Plan.findOne({ stripeSubscriptionId: subscriptionId });
        if (!plan) {
            console.error('Plan not found for subscription:', subscriptionId);
            return;
        }

        await Plan.findOneAndUpdate(
            { stripeSubscriptionId: subscriptionId },
            {
                subscriptionStatus: 'canceled',
                isPlanActive: false,
                planExpires: new Date()
            }
        );

        console.log('Subscription deleted:', subscriptionId);
    } catch (error) {
        console.error('Error handling subscription deleted:', error);
    }
};

// Manual plan change (for testing/admin purposes)
const changePlanType = asyncHandler(async (req, res) => {
    try {
        const { planType } = req.body;
        const userId = req.user.userId;

        // Validate plan type
        if (!['free', 'basic', 'premium'].includes(planType)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid plan type. Must be: free, basic, or premium'
            });
        }

        // Get current user
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        const oldPlanType = user.planType;

        // Update user's plan type
        await User.findByIdAndUpdate(userId, {
            planType: planType,
            isPlan: planType !== 'free'
        });

        // Reset usage for new plan
        await resetUsageOnPlanChange(userId, planType);

        console.log(`Manual plan change: User ${userId} changed from ${oldPlanType} to ${planType}`);

        res.status(200).json({
            success: true,
            message: `Plan changed successfully from ${oldPlanType} to ${planType}`,
            data: {
                oldPlanType,
                newPlanType: planType,
                usageReset: true
            }
        });

    } catch (error) {
        console.error('Change plan type error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to change plan type',
            error: error.message
        });
    }
});

module.exports = {
    createSubscription,
    cancelSubscription,
    getSubscriptionStatus,
    handleWebhook,
    changePlanType
};
