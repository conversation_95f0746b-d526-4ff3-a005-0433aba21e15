# Summarize API Documentation

## Overview
This document provides comprehensive information about the summarize system with usage limits based on user plans.

## Base URL
```
http://localhost:3000/api/summarize
```

## Plan Limits
- **Free Plan**: 3 summaries per month
- **Basic Plan**: 20 summaries per month  
- **Premium Plan**: Unlimited summaries

## Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### 1. Generate Summary

**Endpoint:** `POST /api/summarize/summarize`
**Description:** Generate a summary from text or uploaded file
**Headers:** `Authorization: Bearer <token>`

**Request Body (Text):**
```json
{
  "text": "Your text content to summarize..."
}
```

**Request Body (File Upload):**
- Form data with file field named "file"
- Supported formats: PDF, DOCX, TXT

**Success Response (200):**
```json
{
  "success": true,
  "summary": "Generated summary text...",
  "usage": {
    "planType": "basic",
    "used": 5,
    "remaining": 15,
    "limit": 20
  }
}
```

**Error Response - Limit Exceeded (429):**
```json
{
  "success": false,
  "error": "Monthly summary limit exceeded",
  "data": {
    "planType": "free",
    "limit": 3,
    "used": 3,
    "remaining": 0
  }
}
```

### 2. Get Usage Status

**Endpoint:** `GET /api/summarize/usage`
**Description:** Get current usage statistics for the authenticated user
**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "data": {
    "planType": "basic",
    "used": 5,
    "remaining": 15,
    "limit": 20,
    "canSummarize": true
  }
}
```

**Premium User Response:**
```json
{
  "success": true,
  "data": {
    "planType": "premium",
    "used": 50,
    "remaining": "unlimited",
    "limit": "unlimited",
    "canSummarize": true
  }
}
```

**Free User at Limit:**
```json
{
  "success": true,
  "data": {
    "planType": "free",
    "used": 3,
    "remaining": 0,
    "limit": 3,
    "canSummarize": false
  }
}
```

## Error Responses

### Authentication Required (401)
```json
{
  "success": false,
  "error": "Authentication required"
}
```

### User Not Found (404)
```json
{
  "success": false,
  "error": "User not found"
}
```

### No Content Provided (400)
```json
{
  "success": false,
  "error": "No text or file provided"
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "Failed to generate summary"
}
```

## Usage Tracking
- Usage is tracked monthly (resets every month)
- Each successful summary generation counts as 1 usage
- Failed requests do not count towards usage
- Usage limits are enforced before processing to prevent unnecessary API calls

## Frontend Integration Example

```javascript
// Check usage before showing summarize button
const checkUsage = async () => {
  const response = await fetch('/api/summarize/usage', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const data = await response.json();
  
  if (!data.data.canSummarize) {
    // Show upgrade plan message
    showUpgradeMessage(data.data);
  } else {
    // Enable summarize functionality
    enableSummarize(data.data.remaining);
  }
};

// Generate summary with usage feedback
const generateSummary = async (text) => {
  const response = await fetch('/api/summarize/summarize', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ text })
  });
  
  const data = await response.json();
  
  if (data.success) {
    // Show summary and update usage display
    displaySummary(data.summary);
    updateUsageDisplay(data.usage);
  } else if (response.status === 429) {
    // Show limit exceeded message
    showLimitExceeded(data.data);
  }
};
```
