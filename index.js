const express = require('express');
const app = express();
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
dotenv.config();
const connection = require('./config/connection');
const summarizeRoutes = require('./routes/summarize');
const authRoutes = require('./routes/auth');
const subscriptionRoutes = require('./routes/subscription');
const cors = require('cors');

const port = process.env.PORT || 3000;

// Database connection
connection();

// Middleware
app.use(cors());

// Stripe webhook route (must be before body parser)
app.use('/api/subscription/webhook', express.raw({ type: 'application/json' }));

app.use(bodyParser.json({ limit: '10mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));



// Routes
app.use('/api/auth', authRoutes);
app.use('/api/summarize', summarizeRoutes);
app.use('/api/subscription', subscriptionRoutes);

app.listen(port, () => {
  console.log(`Server is running on http://localhost:${port}`);
});

