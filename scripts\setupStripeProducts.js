const dotenv = require('dotenv');
dotenv.config();

const stripeService = require('../services/stripeService');

async function setupStripeProducts() {
    try {
        console.log('Setting up Stripe products and prices...');
        
        const products = await stripeService.createProducts();
        
        console.log('\n✅ Products and prices created successfully!');
        console.log('\n📋 Copy these Price IDs to your .env file:');
        console.log(`STRIPE_BASIC_PRICE_ID=${products.basic.price.id}`);
        console.log(`STRIPE_PREMIUM_PRICE_ID=${products.premium.price.id}`);
        
        console.log('\n📦 Product Details:');
        console.log('Basic Plan:');
        console.log(`  Product ID: ${products.basic.product.id}`);
        console.log(`  Price ID: ${products.basic.price.id}`);
        console.log(`  Amount: £${products.basic.price.unit_amount / 100}/month`);
        
        console.log('\nPremium Plan:');
        console.log(`  Product ID: ${products.premium.product.id}`);
        console.log(`  Price ID: ${products.premium.price.id}`);
        console.log(`  Amount: £${products.premium.price.unit_amount / 100}/month`);
        
        console.log('\n🔧 Next steps:');
        console.log('1. Update your .env file with the Price IDs shown above');
        console.log('2. Set up your Stripe webhook endpoint in the Stripe Dashboard');
        console.log('3. Add the webhook secret to your .env file');
        console.log('4. Test the subscription flow');
        
    } catch (error) {
        console.error('❌ Error setting up Stripe products:', error.message);
        process.exit(1);
    }
}

// Run the setup if this file is executed directly
if (require.main === module) {
    setupStripeProducts();
}

module.exports = setupStripeProducts;
