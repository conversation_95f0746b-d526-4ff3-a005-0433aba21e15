const express = require('express');
const router = express.Router();
const {
    signup,
    verifyEmail,
    login,
    forgotPassword,
    verifyResetLink,
    resetPassword,
    resendVerification,
    getProfile,
    enableUserPlan
} = require('../controllers/authController');
const verifyToken = require('../middlewere/verifyToken');


// Public routes
router.post('/signup', signup);
router.post('/verify-email', verifyEmail);
router.post('/login', login);
router.post('/forgot-password', forgotPassword);
router.get('/verify-reset-link', verifyResetLink);
router.post('/reset-password', resetPassword);
router.post('/resend-verification', resendVerification);

// Protected routes
router.get('/profile', verifyToken, getProfile);
router.post('/enable-plan', verifyToken, enableUserPlan);

module.exports = router;
