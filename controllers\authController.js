const User = require('../models/User');
const emailService = require('../services/emailService');
const asyncHandler = require('express-async-handler');
const jwt = require('jsonwebtoken');
const randomString = require('randomstring');
const bcrypt = require('bcryptjs');

// token generator function
const tokenGenerator = (userId) => {
  // Generate JWT token
  return jwt.sign(
    { userId: userId }, // You can include more user data here if needed
    process.env.JWT_SECRET || 'your_jwt_secret_here',
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};


function generateVerificationCode() {
  return randomString.generate({
    length: 4,
    charset: "alphanumeric",
  });
}


const signup = asyncHandler(async (req, res) => {
    const { firstname, lastname, email, password } = req.body;

    // Validation
    if (!firstname || !lastname || !email || !password) {
        return res.status(400).json({
            success: false,
            message: 'Please provide all required fields: firstname, lastname, email, password'
        });
    }

    // Check if user already exists
    const existingUser = await User.findOne({email});
    if (existingUser) {
        return res.status(400).json({
            success: false,
            message: 'User with this email already exists'
        });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);
    

    // Create user
    const user = new User({
        firstname,
        lastname,
        email,
        password:hashedPassword
    });

    

    // Generate email verification code
    const verificationCode=generateVerificationCode();
    user.emailVerificationCode = verificationCode;
    user.emailVerificationExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
    
    // Save user
    await user.save();

    // Send verification email
    try {
        await emailService.sendEmailVerification(email, firstname, verificationCode);
        
        res.status(201).json({
            success: true,
            message: 'User registered successfully. Please check your email for verification code.',
            data: {
                userId: user._id,
                email: user.email,
                firstname: user.firstname,
                lastname: user.lastname,
                isEmailVerified: user.isEmailVerified,
                isPlan: user.isPlan,
                planType: user.planType
            }
        });
    } catch (emailError) {
        // If email fails, delete the user
        await User.findByIdAndDelete(user._id);
        console.log("email error:", emailError);
        

        throw new Error('Failed to send verification email. Please try again.');
    }
});

const verifyEmail = asyncHandler(async (req, res) => {
    const { email, verificationCode } = req.body;

    if (!email || !verificationCode) {
        return res.status(400).json({
            success: false,
            message: 'Email and verification code are required'
        });
    }

    // Find user
    const user = await User.findOne({email});
    if (!user) {
        return res.status(404).json({
            success: false,
            message: 'User not found'
        });
    }

    // Check if already verified
    if (user.isEmailVerified) {
        return res.status(400).json({
            success: false,
            message: 'Email is already verified'
        });
    }

    // Verify code
    if (user.emailVerificationCode !== verificationCode || user.emailVerificationExpires < Date.now()) {
        return res.status(400).json({
            success: false,
            message: 'Invalid or expired verification code'
        });
    }

    // Update user
    user.isEmailVerified = true;
    user.emailVerificationCode = null;
    user.emailVerificationExpires = null;
    await user.save();

    // Generate token
    const token = tokenGenerator(user._id);

    res.status(200).json({
        success: true,
        message: 'Email verified successfully',
        data: {
            token,
            user: {
                id: user._id,
                firstname: user.firstname,
                lastname: user.lastname,
                email: user.email,
                isEmailVerified: user.isEmailVerified,
                role: user.role,
                isPlan: user.isPlan,
                planType: user.planType
            }
        }
    });
});


const login = asyncHandler(async (req, res) => {
    const { email, password } = req.body;

    if (!email || !password) {
        return res.status(400).json({
            success: false,
            message: 'Email and password are required'
        });
    }

    // Find user and include password for comparison
    const user = await User.findOne({email});
    if (!user) {
        return res.status(401).json({
            success: false,
            message: 'Invalid email or password'
        });
    }

    const isMatch = await bcrypt.compare(password, user.password);
    
    if (!isMatch) {
        return res.status(401).json({
            success: false,
            message: 'Invalid email or password'
        });
    }

    // Check if email is verified
    if (!user.isEmailVerified) {
        return res.status(401).json({
            success: false,
            message: 'Please verify your email address before logging in'
        });
    }

    // Generate token
    const token = tokenGenerator(user._id);

    res.status(200).json({
        success: true,
        message: 'Login successful',
        data: {
            token,
            user: {
                id: user._id,
                firstname: user.firstname,
                lastname: user.lastname,
                email: user.email,
                isEmailVerified: user.isEmailVerified,
                role: user.role,
                isPlan: user.isPlan,
                planType: user.planType
            }
        }
    });
});


const forgotPassword = asyncHandler(async (req, res) => {
    const { email } = req.body;

    if (!email) {
        return res.status(400).json({
            success: false,
            message: 'Email is required'
        });
    }

    // Find user
    const user = await User.findOne({email});
    if (!user) {
        return res.status(404).json({
            success: false,
            message: 'User with this email does not exist'
        });
    }

    // Generate reset code
    const resetCode = generateVerificationCode();
    user.passwordResetCode = resetCode;
    user.passwordResetExpires = Date.now() + 30 * 60 * 1000; // 30 minutes
    await user.save();

    // Create reset link with code and email as query params - BACKEND URL
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:3000';
    const resetLink = `${backendUrl}/api/auth/verify-reset-link?code=${resetCode}&email=${encodeURIComponent(email)}`;

    // Send reset email with link
    await emailService.sendPasswordResetLink(email, user.firstname, resetLink);

    res.status(200).json({
        success: true,
        message: 'Password reset link sent to your email'
    });
});

// New API to verify reset link
const verifyResetLink = asyncHandler(async (req, res) => {
    const { code, email } = req.query;

    if (!code || !email) {
        return res.status(400).json({
            success: false,
            message: 'Code and email are required'
        });
    }

    // Find user
    const user = await User.findOne({email});
    if (!user) {
        return res.status(404).json({
            success: false,
            message: 'User not found'
        });
    }

    // Verify reset code
    if (user.passwordResetCode !== code || user.passwordResetExpires < Date.now()) {
        return res.status(400).json({
            success: false,
            message: 'Invalid or expired reset link'
        });
    }

    // If valid, redirect to frontend reset password page
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const resetPageUrl = `${frontendUrl}/reset-password?code=${code}&email=${encodeURIComponent(email)}`;

    // You can either redirect or return success
    res.redirect(resetPageUrl);
});


// New API to reset password after verification
const resetPassword = asyncHandler(async (req, res) => {
    const { email, code, newPassword } = req.body;

    if (!email || !code || !newPassword) {
        return res.status(400).json({
            success: false,
            message: 'Email, code, and new password are required'
        });
    }

    if (newPassword.length < 6) {
        return res.status(400).json({
            success: false,
            message: 'Password must be at least 6 characters long'
        });
    }

    // Find user
    const user = await User.findOne({email});
    if (!user) {
        return res.status(404).json({
            success: false,
            message: 'User not found'
        });
    }

    // Verify reset code
    if (user.passwordResetCode !== code || user.passwordResetExpires < Date.now()) {
        return res.status(400).json({
            success: false,
            message: 'Invalid or expired reset code'
        });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password
    user.password = hashedPassword;
    user.passwordResetCode = null;
    user.passwordResetExpires = null;
    await user.save();

    res.status(200).json({
        success: true,
        message: 'Password reset successfully'
    });
});


const resendVerification = asyncHandler(async (req, res) => {
    const { email } = req.body;

    if (!email) {
        return res.status(400).json({
            success: false,
            message: 'Email is required'
        });
    }

    // Find user
    const user = await User.findOne({email});
    if (!user) {
        return res.status(404).json({
            success: false,
            message: 'User not found'
        });
    }

    // Check if already verified
    if (user.isEmailVerified) {
        return res.status(400).json({
            success: false,
            message: 'Email is already verified'
        });
    }

    // Generate new verification code
    const verificationCode = generateVerificationCode();
    user.emailVerificationCode = verificationCode;
    user.emailVerificationExpires = Date.now() + 10 * 60 * 1000; // 10 minutes
    await user.save();

    // Send verification email
    await emailService.sendEmailVerification(email, user.firstname, verificationCode);

    res.status(200).json({
        success: true,
        message: 'Verification email sent successfully'
    });
});

// Get user profile
const getProfile = asyncHandler(async (req, res) => {
    try {
        const userId = req.user.userId;

        const user = await User.findById(userId).select('-password -emailVerificationCode -passwordResetCode');

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        res.status(200).json({
            success: true,
            message: 'Profile retrieved successfully',
            data: {
                user: {
                    id: user._id,
                    firstname: user.firstname,
                    lastname: user.lastname,
                    email: user.email,
                    isEmailVerified: user.isEmailVerified,
                    role: user.role,
                    isPlan: user.isPlan,
                    planType: user.planType,
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt
                }
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Server error'
        });
    }
});

// Enable plan for user (for testing/admin purposes)
const enableUserPlan = asyncHandler(async (req, res) => {
    try {
        const userId = req.user.userId;

        // Get current user
        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // Update user's isPlan to true
        const updatedUser = await User.findByIdAndUpdate(
            userId,
            { isPlan: true },
            { new: true }
        );

        res.status(200).json({
            success: true,
            message: 'User plan enabled successfully',
            data: {
                userId: updatedUser._id,
                email: updatedUser.email,
                isPlan: updatedUser.isPlan,
                planType: updatedUser.planType
            }
        });

    } catch (error) {
        console.error('Enable user plan error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to enable user plan',
            error: error.message
        });
    }
});

module.exports = {
    signup,
    verifyEmail,
    login,
    forgotPassword,
    verifyResetLink,
    enableUserPlan,
    resetPassword,
    resendVerification,
    getProfile
};
