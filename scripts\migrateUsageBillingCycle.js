const mongoose = require('mongoose');
const User = require('../models/User');
const Plan = require('../models/Plan');
const Usage = require('../models/Usage');
require('dotenv').config();

// Migration script to update usage records with billing cycle dates
const migrateUsageBillingCycle = async () => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        // Get all existing usage records
        const existingUsages = await Usage.find({});
        console.log(`Found ${existingUsages.length} existing usage records`);

        for (const usage of existingUsages) {
            const user = await User.findById(usage.userId);
            if (!user) {
                console.log(`User not found for usage record: ${usage._id}`);
                continue;
            }

            let updateData = {};

            if (user.planType === 'free') {
                // Free users: set monthly billing cycle
                if (usage.month && usage.year) {
                    const [year, month] = usage.month.split('-');
                    const cycleStart = new Date(parseInt(year), parseInt(month) - 1, 1);
                    const cycleEnd = new Date(parseInt(year), parseInt(month), 0, 23, 59, 59, 999);
                    
                    updateData.billingCycleStart = cycleStart;
                    updateData.billingCycleEnd = cycleEnd;
                } else {
                    // Fallback to current month
                    const now = new Date();
                    const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
                    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
                    
                    updateData.billingCycleStart = cycleStart;
                    updateData.billingCycleEnd = cycleEnd;
                    updateData.month = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
                    updateData.year = now.getFullYear();
                }
            } else {
                // Paid users: get billing cycle from plan
                const plan = await Plan.findOne({ userId: usage.userId, isPlanActive: true });
                
                if (plan && plan.currentPeriodStart && plan.currentPeriodEnd) {
                    updateData.billingCycleStart = plan.currentPeriodStart;
                    updateData.billingCycleEnd = plan.currentPeriodEnd;
                } else {
                    // Fallback to monthly if no active plan
                    const now = new Date();
                    const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
                    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
                    
                    updateData.billingCycleStart = cycleStart;
                    updateData.billingCycleEnd = cycleEnd;
                }
            }

            // Update the usage record
            await Usage.findByIdAndUpdate(usage._id, updateData);
            console.log(`Updated usage record for user ${usage.userId} (${user.planType} plan)`);
        }

        console.log('Migration completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    }
};

// Run migration if this file is executed directly
if (require.main === module) {
    migrateUsageBillingCycle();
}

module.exports = migrateUsageBillingCycle;
