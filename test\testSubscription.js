const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:3000/api';
let authToken = '';

// Test user credentials (use existing user or create new one)
const testUser = {
    email: '<EMAIL>',
    password: 'testpassword123'
};

async function runTests() {
    try {
        console.log('🚀 Starting Subscription API Tests...\n');

        // Step 1: Login to get JWT token
        console.log('1️⃣ Logging in...');
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, testUser);
        
        if (loginResponse.data.success) {
            authToken = loginResponse.data.token;
            console.log('✅ Login successful');
        } else {
            console.log('❌ Login failed. Please check user credentials.');
            return;
        }

        // Step 2: Check current subscription status
        console.log('\n2️⃣ Checking current subscription status...');
        const statusResponse = await axios.get(`${BASE_URL}/subscription/status`, {
            headers: { Authorization: `Bearer ${authToken}` }
        });
        
        console.log('Current Status:', statusResponse.data.data);

        // Step 3: Create Basic Plan subscription
        console.log('\n3️⃣ Creating Basic Plan subscription...');
        const subscriptionResponse = await axios.post(`${BASE_URL}/subscription/create`, {
            planType: 'basic',
            successUrl: 'http://localhost:3000/success',
            cancelUrl: 'http://localhost:3000/cancel'
        }, {
            headers: { Authorization: `Bearer ${authToken}` }
        });

        if (subscriptionResponse.data.success) {
            console.log('✅ Subscription created successfully!');
            console.log('🔗 Checkout URL:', subscriptionResponse.data.data.url);
            console.log('\n📋 Next Steps:');
            console.log('1. Open the checkout URL in browser');
            console.log('2. Use test card: 4242 4242 4242 4242');
            console.log('3. Complete the payment');
            console.log('4. Check subscription status again');
        } else {
            console.log('❌ Subscription creation failed:', subscriptionResponse.data.message);
        }

        // Step 4: Test Premium Plan
        console.log('\n4️⃣ Testing Premium Plan creation...');
        try {
            const premiumResponse = await axios.post(`${BASE_URL}/subscription/create`, {
                planType: 'premium'
            }, {
                headers: { Authorization: `Bearer ${authToken}` }
            });
            
            if (premiumResponse.data.success) {
                console.log('✅ Premium subscription URL:', premiumResponse.data.data.url);
            }
        } catch (error) {
            if (error.response?.status === 400) {
                console.log('✅ Expected error: User already has active subscription');
            } else {
                console.log('❌ Unexpected error:', error.response?.data?.message);
            }
        }

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
    }
}

// Test card information
console.log('💳 Test Card Information:');
console.log('Card Number: 4242 4242 4242 4242');
console.log('Expiry: 12/25');
console.log('CVC: 123');
console.log('ZIP: 12345\n');

// Run tests
runTests();
