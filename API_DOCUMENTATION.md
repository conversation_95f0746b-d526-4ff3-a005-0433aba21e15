# Kintess V2 Backend - Authentication API Documentation

## Overview
This document provides comprehensive information about the authentication system implemented in Kintess V2 Backend.

## Base URL
```
http://localhost:3000/api/auth
```

## Authentication Endpoints

### 1. User Signup
**Endpoint:** `POST /api/auth/signup`
**Description:** Register a new user account

**Request Body:**
```json
{
  "firstname": "<PERSON>",
  "lastname": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Success Response (201):**
```json
{
  "success": true,
  "message": "User registered successfully. Please check your email for verification code.",
  "data": {
    "userId": "64f8a1b2c3d4e5f6g7h8i9j0",
    "email": "<EMAIL>",
    "firstname": "<PERSON>",
    "lastname": "<PERSON><PERSON>",
    "isEmailVerified": false
  }
}
```

### 2. Email Verification
**Endpoint:** `POST /api/auth/verify-email`
**Description:** Verify user email with the code sent via email

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "verificationCode": "123456"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Email verified successfully",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "64f8a1b2c3d4e5f6g7h8i9j0",
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "isEmailVerified": true,
      "role": "user"
    }
  }
}
```

### 3. User Login
**Endpoint:** `POST /api/auth/login`
**Description:** Login with email and password

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "64f8a1b2c3d4e5f6g7h8i9j0",
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "isEmailVerified": true,
      "role": "user"
    }
  }
}
```

### 4. Forgot Password
**Endpoint:** `POST /api/auth/forgot-password`
**Description:** Request password reset code via email

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset code sent to your email"
}
```

### 5. Reset Password
**Endpoint:** `POST /api/auth/reset-password`
**Description:** Reset password using the code sent via email

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "resetCode": "123456",
  "newPassword": "newpassword123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

### 6. Resend Verification Email
**Endpoint:** `POST /api/auth/resend-verification`
**Description:** Resend email verification code

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Verification email sent successfully"
}
```

## Protected Endpoints (Require Authentication)

### 7. Get User Profile
**Endpoint:** `GET /api/auth/profile`
**Description:** Get current user profile
**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "user": {
      "id": "64f8a1b2c3d4e5f6g7h8i9j0",
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "isEmailVerified": true,
      "role": "user",
      "createdAt": "2023-09-06T10:30:00.000Z",
      "updatedAt": "2023-09-06T10:35:00.000Z"
    }
  }
}
```

### 8. Verify Token
**Endpoint:** `GET /api/auth/verify-token`
**Description:** Verify if the provided token is valid
**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Token is valid",
  "data": {
    "user": {
      "id": "64f8a1b2c3d4e5f6g7h8i9j0",
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "isEmailVerified": true,
      "role": "user"
    }
  }
}
```

### 9. Logout
**Endpoint:** `POST /api/auth/logout`
**Description:** Logout user (client should remove token)
**Headers:** `Authorization: Bearer <token>`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Logged out successfully. Please remove the token from client storage."
}
```

## Error Responses

### Common Error Codes:
- **400 Bad Request:** Invalid input data
- **401 Unauthorized:** Invalid credentials or token
- **404 Not Found:** User not found
- **500 Internal Server Error:** Server error

### Example Error Response:
```json
{
  "success": false,
  "message": "Invalid email or password"
}
```

## Environment Variables Required

Create a `.env` file with the following variables:

```env
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGO_URI=mongodb://localhost:27017/kintess_v2

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_complex
JWT_EXPIRES_IN=7d

# SendGrid Email Configuration
SENDGRID_API_KEY=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>
FROM_NAME=Kintess
```

## Authentication Flow

1. **Signup:** User registers with firstname, lastname, email, and password
2. **Email Verification:** User receives verification code via email and verifies
3. **Login:** User can now login and receive JWT token
4. **Protected Routes:** Use JWT token in Authorization header for protected routes

## Security Features

- Password hashing using bcryptjs
- JWT token-based authentication
- Email verification required before login
- Password reset with time-limited codes
- Input validation and sanitization
- Rate limiting (recommended to add)

## Testing the API

You can test the API using tools like Postman, curl, or any HTTP client. Make sure to:

1. Set up your MongoDB database
2. Configure SendGrid API key
3. Set all required environment variables
4. Start the server with `npm start` or `nodemon index.js`
