# Subscription API Documentation

This document describes the Stripe subscription-based payment API endpoints for the <PERSON><PERSON><PERSON> application.

## Overview

The application supports 3 pricing tiers:
- **Free Plan**: £0/month (default)
- **Basic Plan**: £9.99/month
- **Premium Plan**: £19.99/month

## Authentication

All subscription endpoints (except webhooks) require authentication via JW<PERSON> token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Endpoints

### 1. Create Subscription

Creates a new subscription for the authenticated user.

**Endpoint:** `POST /api/subscription/create`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "planType": "basic", // or "premium"
  "successUrl": "https://yourapp.com/success", // optional
  "cancelUrl": "https://yourapp.com/cancel"    // optional
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Checkout session created successfully",
  "data": {
    "sessionId": "cs_test_...",
    "url": "https://checkout.stripe.com/pay/cs_test_..."
  }
}
```

**Response (Error):**
```json
{
  "success": false,
  "message": "Invalid plan type. Must be basic or premium."
}
```

### 2. Cancel Subscription

Cancels the user's active subscription.

**Endpoint:** `POST /api/subscription/cancel`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "immediate": false // optional, defaults to false (cancel at period end)
}
```

**Response (Success):**
```json
{
  "success": true,
  "message": "Subscription will cancel at period end",
  "data": {
    "subscriptionId": "sub_...",
    "status": "active",
    "cancelAtPeriodEnd": true,
    "currentPeriodEnd": 1640995200
  }
}
```

### 3. Get Subscription Status

Retrieves the current subscription status for the authenticated user.

**Endpoint:** `GET /api/subscription/status`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response (Success):**
```json
{
  "success": true,
  "data": {
    "planType": "basic",
    "planAmount": 9.99,
    "isPlanActive": true,
    "subscriptionStatus": "active",
    "planStartDate": "2024-01-01T00:00:00.000Z",
    "planExpires": "2024-02-01T00:00:00.000Z",
    "currentPeriodStart": "2024-01-01T00:00:00.000Z",
    "currentPeriodEnd": "2024-02-01T00:00:00.000Z"
  }
}
```

### 4. Stripe Webhook

Handles Stripe webhook events for subscription updates.

**Endpoint:** `POST /api/subscription/webhook`

**Headers:**
```
stripe-signature: <webhook_signature>
Content-Type: application/json
```

**Note:** This endpoint is called by Stripe and should not be called directly by your application.

## Setup Instructions

### 1. Environment Variables

Add the following to your `.env` file:

```env
# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs (Get these after running the setup script)
STRIPE_BASIC_PRICE_ID=price_basic_plan_id_here
STRIPE_PREMIUM_PRICE_ID=price_premium_plan_id_here
```

### 2. Create Stripe Products

Run the setup script to create products and prices in Stripe:

```bash
node scripts/setupStripeProducts.js
```

This will output the Price IDs that you need to add to your `.env` file.

### 3. Configure Webhook

1. Go to your Stripe Dashboard
2. Navigate to Developers > Webhooks
3. Create a new webhook endpoint: `https://yourdomain.com/api/subscription/webhook`
4. Select the following events:
   - `checkout.session.completed`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. Copy the webhook secret and add it to your `.env` file

## Frontend Integration

### Basic Flow

1. **Get subscription status** to check current plan
2. **Create subscription** to upgrade to paid plan
3. **Redirect user** to Stripe Checkout using the returned URL
4. **Handle success/cancel** redirects from Stripe
5. **Check subscription status** again to confirm activation

### Example Frontend Code

```javascript
// Create subscription
const createSubscription = async (planType) => {
  const response = await fetch('/api/subscription/create', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      planType: planType,
      successUrl: window.location.origin + '/subscription/success',
      cancelUrl: window.location.origin + '/subscription/cancel'
    })
  });
  
  const data = await response.json();
  if (data.success) {
    // Redirect to Stripe Checkout
    window.location.href = data.data.url;
  }
};

// Check subscription status
const getSubscriptionStatus = async () => {
  const response = await fetch('/api/subscription/status', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  const data = await response.json();
  return data.data;
};
```

## Error Handling

Common error scenarios:

1. **User already has active subscription**: Returns 400 error
2. **Invalid plan type**: Returns 400 error
3. **No active subscription to cancel**: Returns 404 error
4. **Stripe API errors**: Returns 500 error with details
5. **Authentication required**: Returns 401 error

## Testing

Use Stripe's test mode with test card numbers:
- **Success**: 4242 4242 4242 4242
- **Decline**: 4000 0000 0000 0002
- **3D Secure**: 4000 0025 0000 3155

## Security Notes

1. Never expose your Stripe secret key in frontend code
2. Always verify webhook signatures
3. Use HTTPS in production
4. Validate all input parameters
5. Log subscription events for audit purposes
