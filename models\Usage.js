const mongoose = require('mongoose');

const usageSchema = new mongoose.Schema({
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    
    // Billing cycle tracking
    billingCycleStart: {
        type: Date,
        required: true
    },

    billingCycleEnd: {
        type: Date,
        required: true
    },

    // For free users - monthly tracking
    month: {
        type: String, // Format: "2024-01" (YYYY-MM)
        default: null
    },

    year: {
        type: Number,
        default: null
    },
    
    // Summary usage count
    summaryCount: {
        type: Number,
        default: 0
    },
    
    // Last summary date
    lastSummaryDate: {
        type: Date,
        default: null
    },
    
    // Plan type at the time of usage (for historical tracking)
    planType: {
        type: String,
        enum: ['free', 'basic', 'premium'],
        required: true
    }

}, {
    timestamps: true
});

usageSchema.index(
  { userId: 1, month: 1 },
  {
    unique: true,
    partialFilterExpression: { month: { $type: "string" } }
  }
);

module.exports = mongoose.model('Usage', usageSchema);
