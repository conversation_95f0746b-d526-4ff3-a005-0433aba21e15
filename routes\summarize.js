const express = require('express');
const router = express.Router();
const multer = require("multer");
const upload = multer({ dest: "uploads/" });
const verifyToken = require('../middlewere/verifyToken');

const { summarizeText, getUsageStatus } = require('../controllers/summarize');

// Protected routes (require authentication)
router.post("/summarize", verifyToken, upload.single("file"), summarizeText);
router.get("/usage", verifyToken, getUsageStatus);

module.exports = router;