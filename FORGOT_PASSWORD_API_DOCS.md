# Forgot Password API Documentation

## Overview
This document describes the new link-based forgot password system that replaces the old code-based system. The new system sends a reset link via email instead of a verification code.

## API Endpoints

### 1. Request Password Reset
**Endpoint:** `POST /api/auth/forgot-password`
**Description:** Send password reset link to user's email

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset link sent to your email"
}
```

**Error Responses:**
- **400 Bad Request:**
```json
{
  "success": false,
  "message": "Email is required"
}
```

- **404 Not Found:**
```json
{
  "success": false,
  "message": "User with this email does not exist"
}
```

### 2. Verify Reset Link
**Endpoint:** `GET /api/auth/verify-reset-link?code={code}&email={email}`
**Description:** Verify the reset link and redirect to frontend reset form

**Query Parameters:**
- `code`: Reset verification code
- `email`: User's email address

**Success Response:**
- Redirects to: `{FRONTEND_URL}/reset-password-form?code={code}&email={email}`

**Error Responses:**
- **400 Bad Request:**
```json
{
  "success": false,
  "message": "Code and email are required"
}
```

- **404 Not Found:**
```json
{
  "success": false,
  "message": "User not found"
}
```

- **400 Bad Request:**
```json
{
  "success": false,
  "message": "Invalid or expired reset link"
}
```

### 3. Reset Password
**Endpoint:** `POST /api/auth/reset-password`
**Description:** Update user's password after verification

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "code": "ABC123",
  "newPassword": "newSecurePassword123"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Password reset successfully"
}
```

**Error Responses:**
- **400 Bad Request:**
```json
{
  "success": false,
  "message": "Email, code, and new password are required"
}
```

- **400 Bad Request:**
```json
{
  "success": false,
  "message": "Password must be at least 6 characters long"
}
```

- **404 Not Found:**
```json
{
  "success": false,
  "message": "User not found"
}
```

- **400 Bad Request:**
```json
{
  "success": false,
  "message": "Invalid or expired reset code"
}
```

## Flow Description

### 1. User Requests Password Reset
- User enters email on forgot password page
- Frontend calls `POST /api/auth/forgot-password`
- Backend generates reset code and saves to user record
- Backend sends email with reset link containing code and email as query params
- Link format: `{FRONTEND_URL}/reset-password?code={code}&email={email}`

### 2. User Clicks Reset Link
- User clicks link in email
- Browser navigates to `GET /api/auth/verify-reset-link?code={code}&email={email}`
- Backend verifies code and email
- If valid, redirects to `{FRONTEND_URL}/reset-password-form?code={code}&email={email}`
- If invalid, returns error response

### 3. User Resets Password
- Frontend reset form captures new password
- Frontend calls `POST /api/auth/reset-password` with email, code, and new password
- Backend verifies code again and updates password
- Reset code is cleared from user record

## Environment Variables

Add the following to your `.env` file:
```
FRONTEND_URL=http://localhost:3000
```

## Email Template

The system sends an HTML email with:
- Professional styling
- Reset button linking to verification endpoint
- Fallback text link
- 30-minute expiration notice
- Security disclaimer

## Security Features

- Reset codes expire after 30 minutes
- Codes are single-use (cleared after successful reset)
- Email verification before password change
- Secure password hashing with bcrypt
- Input validation and sanitization

## Frontend Integration

### Reset Password Page (`/reset-password`)
This page should:
1. Extract code and email from URL query params
2. Make GET request to verify-reset-link endpoint
3. Handle redirect to reset form

### Reset Password Form (`/reset-password-form`)
This page should:
1. Extract code and email from URL query params
2. Display password reset form
3. Submit new password along with code and email to reset-password endpoint
4. Handle success/error responses
5. Redirect to login page on success
