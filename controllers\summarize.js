const asyncHandler = require("express-async-handler");
const fs = require("fs");
const pdfParse = require("pdf-parse");
const mammoth = require("mammoth");
const User = require('../models/User');
const Usage = require('../models/Usage');
const Plan = require('../models/Plan');


// Initialize OpenAI
const OpenAI = require("openai");

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY, // Make sure it's in .env
  timeout: 300_000,
});

// Plan limits configuration
const PLAN_LIMITS = {
  free: 3,      // 3 summaries per month
  basic: 20,    // 20 summaries per month
  premium: -1   // Unlimited (-1 means no limit)
};

// Helper function to get billing cycle dates
const getBillingCycleDates = async (userId, planType) => {
  const now = new Date();

  if (planType == 'free') {
    // Free users: monthly reset (1st to last day of month)
    const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    return { cycleStart, cycleEnd };
  } else {
    // Paid users: billing cycle based on subscription start date
    const plan = await Plan.findOne({ userId, isPlanActive: true });
    console.log("i am plan ",plan);
    plan.currentPeriodStart
    

    return {
      cycleStart: plan.currentPeriodStart,
      cycleEnd: plan.currentPeriodEnd
    };
  }
};

// Helper function to check usage limits
const checkUsageLimit = async (userId, planType) => {
  const now = new Date();

  // First check if any usage record exists for this user with different plan type
  const existingUsage = await Usage.findOne({ userId });

  if (existingUsage && existingUsage.planType !== planType) {
    console.log(`Plan type mismatch detected for user ${userId}: usage.planType=${existingUsage.planType}, current.planType=${planType}`);
    console.log(`Deleting old usage records and creating fresh record for ${planType} plan`);

    // Delete ALL usage records for this user (regardless of billing cycle)
    await Usage.deleteMany({ userId });
  }

  const { cycleStart, cycleEnd } = await getBillingCycleDates(userId, planType);

  // Find current billing cycle usage
  let usage = await Usage.findOne({
    userId,
    billingCycleStart: cycleStart,
    billingCycleEnd: cycleEnd,
    planType: planType // Also match plan type
  });

  if (!usage) {
    // Create new usage record for current billing cycle
    const usageData = {
      userId,
      billingCycleStart: cycleStart,
      billingCycleEnd: cycleEnd,
      summaryCount: 0,
      planType
    };

    // Add month/year for free users
    if (planType === 'free') {
      usageData.month = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
      usageData.year = now.getFullYear();
    }

    usage = new Usage(usageData);
    await usage.save();

    console.log(`New usage record created for user ${userId} with plan ${planType}`);
    console.log(`Billing cycle: ${cycleStart} to ${cycleEnd}`);
  }

  // Check if user has exceeded limit
  const limit = PLAN_LIMITS[planType];

  if (limit === -1) {
    // Unlimited plan
    return { allowed: true, remaining: -1, used: usage.summaryCount };
  }

  if (usage.summaryCount >= limit) {
    return {
      allowed: false,
      remaining: 0,
      used: usage.summaryCount,
      limit: limit,
      cycleEnd: cycleEnd
    };
  }

  return {
    allowed: true,
    remaining: limit - usage.summaryCount,
    used: usage.summaryCount,
    limit: limit,
    cycleEnd: cycleEnd
  };
};

// Helper function to increment usage count
const incrementUsage = async (userId, planType) => {
  const now = new Date();
  const { cycleStart, cycleEnd } = await getBillingCycleDates(userId, planType);

  await Usage.findOneAndUpdate(
    {
      userId,
      billingCycleStart: cycleStart,
      billingCycleEnd: cycleEnd
    },
    {
      $inc: { summaryCount: 1 },
      $set: {
        lastSummaryDate: now,
        planType: planType
      }
    },
    { upsert: true }
  );
};

// Helper function to reset usage when plan changes
const resetUsageOnPlanChange = async (userId, newPlanType) => {
  const now = new Date();
  const { cycleStart, cycleEnd } = await getBillingCycleDates(userId, newPlanType);

  // Create new usage record for the new plan
  const usageData = {
    userId,
    billingCycleStart: cycleStart,
    billingCycleEnd: cycleEnd,
    summaryCount: 0,
    planType: newPlanType,
    lastSummaryDate: null
  };

  // Add month/year for free users
  if (newPlanType === 'free') {
    usageData.month = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    usageData.year = now.getFullYear();
  }

  // Remove old usage records and create new one
  await Usage.deleteMany({ userId });
  const newUsage = new Usage(usageData);
  await newUsage.save();

  console.log(`Usage reset for user ${userId} - new plan: ${newPlanType}`);
};

const summarizeText = asyncHandler(async (req, res) => {
    // Check if user is authenticated
    if (!req.user || !req.user.userId) {
        return res.status(401).json({
            success: false,
            error: "Authentication required"
        });
    }

    if(!req.body.text && !req.file) {
        return res.status(400).json({
            success: false,
            error: "No text or file provided"
        });
    }

    try {
        // Get user details to check plan type
        const user = await User.findById(req.user.userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                error: "User not found"
            });
        }

        // Check usage limits
        const usageCheck = await checkUsageLimit(req.user.userId, user.planType);

        if (!usageCheck.allowed) {
            return res.status(429).json({
                success: false,
                error: "Monthly summary limit exceeded",
                data: {
                    planType: user.planType,
                    limit: usageCheck.limit,
                    used: usageCheck.used,
                    remaining: usageCheck.remaining
                }
            });
        }
    // Log request details for debugging
    console.log("Request type:", req.file ? "File upload" : "Text in body");
    if (req.body.text) {
      console.log("Text starts with:", req.body.text.substring(0, 20));
      console.log("Is binary data:", req.body.text.startsWith('PK\x03\x04'));
    }


  let extractedText = "";

  // ✅ Case 1: Text provided in body
  if (req.body.text) {
    // Check if the text is actually binary data (DOCX file sent as text)
    if (req.body.text.startsWith('PK\x03\x04')) {
      try {
        // Convert the string back to buffer and process as DOCX
        const buffer = Buffer.from(req.body.text, 'binary');
        const result = await mammoth.extractRawText({ buffer: buffer });
        extractedText = result.value;
      } catch (error) {
        console.error("Error processing DOCX data:", error);
        return res.status(400).json({ error: "Failed to process document data" });
      }
    } else {
      // Regular text
      extractedText = req.body.text;
    }
  }

  // ✅ Case 2: File is uploaded
  else if (req.file) {
    const file = req.file;
    const filePath = file.path;
    const mimeType = file.mimetype;

    try {
      if (mimeType === "text/plain") {
        extractedText = fs.readFileSync(filePath, "utf-8");
      } else if (mimeType === "application/pdf") {
        const dataBuffer = fs.readFileSync(filePath);
        try {
          const pdfData = await pdfParse(dataBuffer);
          extractedText = pdfData.text;
        } catch (pdfError) {
          console.error("PDF parsing error:", pdfError.message);
          throw new Error("Failed to parse PDF file. The PDF may be corrupted, password-protected, or in an unsupported format. Please try with a different PDF file.");
        }
      } else if (
        mimeType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ) {
        try {
          const result = await mammoth.extractRawText({ path: filePath });
          extractedText = result.value;
        } catch (docxError) {
          console.error("DOCX parsing error:", docxError.message);
          throw new Error("Failed to parse DOCX file. The document may be corrupted or in an unsupported format.");
        }
      } else {
        return res.status(400).json({
          success: false,
          error: "Unsupported file type. Please upload PDF, DOCX, or TXT files."
        });
      }
    } catch (fileError) {
      // Clean up file before throwing error
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      throw fileError; // Re-throw to be caught by main catch block
    } finally {
      // ✅ Clean up uploaded file if it still exists
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
  }

  // ❌ Case 3: Neither text nor file
  else {
    return res.status(400).json({
      success: false,
      error: "No text or file provided"
    });
  }

  // Validate extracted text
  if (!extractedText || extractedText.trim().length === 0) {
    return res.status(400).json({
      success: false,
      error: "No text content found in the provided file. Please ensure the file contains readable text."
    });
  }

  // Check minimum text length
  if (extractedText.trim().length < 10) {
    return res.status(400).json({
      success: false,
      error: "Text content is too short to summarize. Please provide more content."
    });
  }

  // 🔁 Send to OpenAI
  const prompt = `You are a professional document summariser trained to extract key insights, obligations, and relevant details with precision and clarity. I will provide a document that may fall into one of the following categories: legal contract, business report or proposal, academic or research paper, real estate document, or a general informational text.
Your task is to generate a structured, professional summary in British English that maintains the integrity and nuance of the original content. The summary should be tailored to professionals in legal, academic, or corporate settings and must reflect a deep understanding of the document type.
Please follow the appropriate summary style based on the type of document:
If Legal Contract:
Identify and outline key clauses.
Summarise responsibilities, obligations, liabilities, and deadlines of each party.
Include payment terms, risk factors, duration, renewal, and termination clauses.
Highlight any unusual or noteworthy provisions.
Use bullet points and headings for clarity.
If Business Report or Proposal:
Begin with a concise executive summary.
Extract key performance indicators, strategic insights, goals, and outcomes.
Highlight financial data, timelines, and recommended next steps.
Include any critical dependencies, risks, or action items.
If Academic or Research Paper:
Summarise the research objective and questions.
Describe the methodology used.
Present the key findings and conclusions.
Mention any implications, applications, or limitations of the study.
If Real Estate Document:
Outline the contract length, rent or purchase price, and financial obligations.
Identify party responsibilities, repair and maintenance terms.
Note break clauses, renewal options, and penalties.
If General Text Document:
Provide an overall summary of the document's main purpose and content.
Break down key themes or sections clearly and logically.
Maintain a formal and neutral tone, preserving the original intent.
Formatting Requirements:
Use clear section headings and bullet points where appropriate.
Maintain a formal tone in British English.
Do not over-simplify — the summary must retain depth and detail for a professional audience.
Ensure accuracy, coherence, and readability throughout.
First, provide a concise paragraph summary capturing the document's purpose, critical information, and overall meaning. Use formal British English and preserve important context.
Then, list the key points from the document as structured bullet points. Bullet points should be and always in the following format:
- Key Point 1
- Key Point 2
- Key Point 3
... Group them by logical or thematic sections where relevant. Use bold section headings if the document has distinct areas.
Preserve important obligations, responsibilities, deadlines, figures, or decisions if present.
Do not simplify the meaning. Keep all essential detail, especially in legal or business contexts.
Text to summarize also provide word count for text to summarize :
${extractedText}
Also provide the word count for ${extractedText}.
`;

// console.log("Extracted text:", extractedText);


// console.log("Prompt sent to OpenAI:", prompt);

        // Smart model selection based on content size
        // Rough estimation: 1 token ≈ 4 characters for English text
        const estimatedTokens = (prompt.length + extractedText.length) / 4;
        const useGPT4 = estimatedTokens > 15000; // Use GPT-4o for large content

        const selectedModel = useGPT4 ? "gpt-4o" : "gpt-3.5-turbo";
        console.log(`Content size: ~${Math.round(estimatedTokens)} tokens, using model: ${selectedModel}`);

        const gptResponse = await openai.chat.completions.create({
            model: selectedModel,
            messages: [{ role: "user", content: prompt }],
            temperature: 0.5,
        });

        const summarizedText = gptResponse.choices[0].message.content;
        console.log("OpenAI response received, length:", summarizedText.length);

        // Increment usage count after successful summary
        await incrementUsage(req.user.userId, user.planType);

        // Get updated usage info
        const updatedUsage = await checkUsageLimit(req.user.userId, user.planType);

        res.status(200).json({
            success: true,
            summary: summarizedText,
            usage: {
                planType: user.planType,
                used: updatedUsage.used,
                remaining: updatedUsage.remaining,
                limit: updatedUsage.limit
            }
        });

    } catch (error) {
        console.error("Error in summarizeText:", error);

        // Handle specific file processing errors
        if (error.message.includes("Failed to parse PDF") ||
            error.message.includes("Failed to parse DOCX") ||
            error.message.includes("bad XRef entry")) {
            return res.status(400).json({
                success: false,
                error: error.message
            });
        }

        // Handle OpenAI API errors
        if (error.code === 'insufficient_quota' || error.code === 'rate_limit_exceeded') {
            return res.status(503).json({
                success: false,
                error: "AI service temporarily unavailable. Please try again later."
            });
        }

        // Handle context length exceeded errors
        if (error.code === 'context_length_exceeded') {
            return res.status(400).json({
                success: false,
                error: "Document is too large to process. Please try with a smaller document or break it into smaller sections."
            });
        }

        // Generic error
        res.status(500).json({
            success: false,
            error: "Failed to generate summary. Please try again."
        });
    }
});


// Get usage status API
const getUsageStatus = asyncHandler(async (req, res) => {
    try {
        // Check if user is authenticated
        if (!req.user || !req.user.userId) {
            return res.status(401).json({
                success: false,
                error: "Authentication required"
            });
        }

        // Get user details
        const user = await User.findById(req.user.userId);
        if (!user) {
            return res.status(404).json({
                success: false,
                error: "User not found"
            });
        }

        // Get current usage
        const usageCheck = await checkUsageLimit(req.user.userId, user.planType);

        res.status(200).json({
            success: true,
            data: {
                planType: user.planType,
                used: usageCheck.used,
                remaining: usageCheck.remaining === -1 ? "unlimited" : usageCheck.remaining,
                limit: usageCheck.limit === -1 ? "unlimited" : usageCheck.limit,
                canSummarize: usageCheck.allowed,
                cycleEnd: usageCheck.cycleEnd
            }
        });

    } catch (error) {
        console.error("Error in getUsageStatus:", error);
        res.status(500).json({
            success: false,
            error: "Failed to get usage status"
        });
    }
});

module.exports = {
    summarizeText,
    getUsageStatus,
    resetUsageOnPlanChange
};