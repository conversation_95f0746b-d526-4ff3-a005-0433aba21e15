const sgMail = require('@sendgrid/mail');

console.log("i am api key:", process.env.SENDGRID_API_KEY);

// Set SendGrid API key
sgMail.setApiKey(process.env.SENDGRID_API_KEY);


class EmailService {
    constructor() {
        this.fromEmail = process.env.FROM_EMAIL;
        this.fromName = process.env.FROM_NAME;
    }

    // Send email verification code
    async sendEmailVerification(email, firstname, verificationCode) {
        const msg = {
            to: email,
            from: {
                email: this.fromEmail,
                name: this.fromName
            },
            subject: 'Verify Your Email Address - Kintess',
            html: `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Email Verification</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; }
                        .content { padding: 20px; background-color: #f9f9f9; }
                        .verification-code { 
                            font-size: 32px; 
                            font-weight: bold; 
                            color: #4CAF50; 
                            text-align: center; 
                            padding: 20px; 
                            background-color: #e8f5e8; 
                            border-radius: 5px; 
                            margin: 20px 0; 
                        }
                        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>Welcome to Kintess!</h1>
                        </div>
                        <div class="content">
                            <h2>Hello ${firstname},</h2>
                            <p>Thank you for signing up with Kintess! To complete your registration, please verify your email address using the verification code below:</p>
                            
                            <div class="verification-code">
                                ${verificationCode}
                            </div>
                            
                            <p><strong>Important:</strong> This verification code will expire in 10 minutes for security reasons.</p>
                            
                            <p>If you didn't create an account with us, please ignore this email.</p>
                            
                            <p>Best regards,<br>The Kintess Team</p>
                        </div>
                        <div class="footer">
                            <p>This is an automated email. Please do not reply to this message.</p>
                        </div>
                    </div>
                </body>
                </html>
            `,
            text: `
                Hello ${firstname},
                
                Thank you for signing up with Kintess! To complete your registration, please verify your email address using the verification code below:
                
                Verification Code: ${verificationCode}
                
                Important: This verification code will expire in 10 minutes for security reasons.
                
                If you didn't create an account with us, please ignore this email.
                
                Best regards,
                The Kintess Team
            `
        };

        try {
            await sgMail.send(msg);
            console.log(`Email verification sent to ${email}`);
            return { success: true, message: 'Verification email sent successfully' };
        } catch (error) {
            console.error('Error sending verification email:', error);
            throw new Error('Failed to send verification email');
        }
    }

    // Send password reset link
    async sendPasswordResetLink(email, firstname, resetLink) {
        const msg = {
            to: email,
            from: {
                email: this.fromEmail,
                name: this.fromName
            },
            subject: 'Reset Your Password - Kintess',
            html: `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Password Reset</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background-color: #FF6B6B; color: white; padding: 20px; text-align: center; }
                        .content { padding: 20px; background-color: #f9f9f9; }
                        .reset-button {
                            display: inline-block;
                            padding: 15px 30px;
                            background-color: #FF6B6B;
                            color: white;
                            text-decoration: none;
                            border-radius: 5px;
                            font-weight: bold;
                            margin: 20px 0;
                        }
                        .footer { background-color: #333; color: white; padding: 10px; text-align: center; font-size: 12px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>Password Reset Request</h1>
                        </div>
                        <div class="content">
                            <p>Hello ${firstname},</p>

                            <p>We received a request to reset your password for your Kintess account. Click the button below to reset your password:</p>

                            <div style="text-align: center;">
                                <a href="${resetLink}" class="reset-button">Reset Password</a>
                            </div>

                            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                            <p style="word-break: break-all; color: #666;">${resetLink}</p>

                            <p><strong>Important:</strong> This reset link will expire in 30 minutes for security reasons.</p>

                            <p>If you didn't request a password reset, please ignore this email. Your password will remain unchanged.</p>

                            <p>Best regards,<br>The Kintess Team</p>
                        </div>
                        <div class="footer">
                            <p>This is an automated email. Please do not reply to this message.</p>
                        </div>
                    </div>
                </body>
                </html>
            `,
            text: `
                Hello ${firstname},

                We received a request to reset your password for your Kintess account.

                Please click on the following link to reset your password:
                ${resetLink}

                Important: This reset link will expire in 30 minutes for security reasons.

                If you didn't request a password reset, please ignore this email. Your password will remain unchanged.

                Best regards,
                The Kintess Team
            `
        };

        try {
            await sgMail.send(msg);
            console.log(`Password reset link sent to ${email}`);
            return { success: true, message: 'Password reset link sent successfully' };
        } catch (error) {
            console.error('Error sending password reset link:', error);
            throw new Error('Failed to send password reset link');
        }
    }

    // Send password reset code (keeping the old method for backward compatibility)
    async sendPasswordReset(email, firstname, resetCode) {
        const msg = {
            to: email,
            from: {
                email: this.fromEmail,
                name: this.fromName
            },
            subject: 'Password Reset Request - Kintess',
            html: `
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="utf-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Password Reset</title>
                    <style>
                        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                        .header { background-color: #FF6B6B; color: white; padding: 20px; text-align: center; }
                        .content { padding: 20px; background-color: #f9f9f9; }
                        .reset-code { 
                            font-size: 32px; 
                            font-weight: bold; 
                            color: #FF6B6B; 
                            text-align: center; 
                            padding: 20px; 
                            background-color: #ffe8e8; 
                            border-radius: 5px; 
                            margin: 20px 0; 
                        }
                        .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
                        .warning { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 15px 0; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>Password Reset Request</h1>
                        </div>
                        <div class="content">
                            <h2>Hello ${firstname},</h2>
                            <p>We received a request to reset your password for your Kintess account. Use the reset code below to create a new password:</p>
                            
                            <div class="reset-code">
                                ${resetCode}
                            </div>
                            
                            <div class="warning">
                                <p><strong>Security Notice:</strong> This reset code will expire in 10 minutes for your security.</p>
                            </div>
                            
                            <p>If you didn't request a password reset, please ignore this email and your password will remain unchanged.</p>
                            
                            <p>Best regards,<br>The Kintess Team</p>
                        </div>
                        <div class="footer">
                            <p>This is an automated email. Please do not reply to this message.</p>
                        </div>
                    </div>
                </body>
                </html>
            `,
            text: `
                Hello ${firstname},
                
                We received a request to reset your password for your Kintess account. Use the reset code below to create a new password:
                
                Reset Code: ${resetCode}
                
                Security Notice: This reset code will expire in 10 minutes for your security.
                
                If you didn't request a password reset, please ignore this email and your password will remain unchanged.
                
                Best regards,
                The Kintess Team
            `
        };

        try {
            await sgMail.send(msg);
            console.log(`Password reset email sent to ${email}`);
            return { success: true, message: 'Password reset email sent successfully' };
        } catch (error) {
            console.error('Error sending password reset email:', error);
            throw new Error('Failed to send password reset email');
        }
    }
}

module.exports = new EmailService();
