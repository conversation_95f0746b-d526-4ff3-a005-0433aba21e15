const mongoose = require('mongoose');
const User = require('../models/User');
const Plan = require('../models/Plan');
const Usage = require('../models/Usage');
require('dotenv').config();

// Script to reset usage for a specific user or all users
const resetUserUsage = async (userEmail = null) => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        let users = [];
        
        if (userEmail) {
            // Reset specific user
            const user = await User.findOne({ email: userEmail });
            if (!user) {
                console.log(`User with email ${userEmail} not found`);
                process.exit(1);
            }
            users = [user];
            console.log(`Resetting usage for user: ${userEmail}`);
        } else {
            // Reset all users
            users = await User.find({});
            console.log(`Resetting usage for all ${users.length} users`);
        }

        for (const user of users) {
            await resetUsageForUser(user._id, user.planType);
            console.log(`✅ Usage reset for ${user.email} (${user.planType} plan)`);
        }

        console.log('✅ Usage reset completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('❌ Usage reset failed:', error);
        process.exit(1);
    }
};

// Helper function to reset usage for a single user
const resetUsageForUser = async (userId, planType) => {
    const now = new Date();
    
    // Remove all existing usage records for this user
    await Usage.deleteMany({ userId });
    
    // Create new usage record based on plan type
    let usageData = {
        userId,
        summaryCount: 0,
        planType: planType,
        lastSummaryDate: null
    };
    
    if (planType === 'free') {
        // Free users: monthly billing cycle
        const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
        
        usageData.billingCycleStart = cycleStart;
        usageData.billingCycleEnd = cycleEnd;
        usageData.month = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
        usageData.year = now.getFullYear();
    } else {
        // Paid users: get billing cycle from plan
        const plan = await Plan.findOne({ userId, isPlanActive: true });
        if (plan && plan.currentPeriodStart && plan.currentPeriodEnd) {
            usageData.billingCycleStart = plan.currentPeriodStart;
            usageData.billingCycleEnd = plan.currentPeriodEnd;
        } else {
            // Fallback to monthly if no active plan
            const cycleStart = new Date(now.getFullYear(), now.getMonth(), 1);
            const cycleEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
            usageData.billingCycleStart = cycleStart;
            usageData.billingCycleEnd = cycleEnd;
        }
    }
    
    const newUsage = new Usage(usageData);
    await newUsage.save();
};

// Get command line arguments
const args = process.argv.slice(2);
const userEmail = args[0]; // Optional: specific user email

// Run script
if (require.main === module) {
    if (userEmail) {
        console.log(`🔄 Resetting usage for user: ${userEmail}`);
        resetUserUsage(userEmail);
    } else {
        console.log('🔄 Resetting usage for all users');
        console.log('To reset specific user: node scripts/resetUserUsage.js <EMAIL>');
        resetUserUsage();
    }
}

module.exports = resetUserUsage;
