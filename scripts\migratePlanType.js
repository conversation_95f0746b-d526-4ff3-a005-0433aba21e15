const mongoose = require('mongoose');
const User = require('../models/User');
const Plan = require('../models/Plan');
require('dotenv').config();

// Migration script to set planType for existing users
const migratePlanType = async () => {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URI);
        console.log('Connected to MongoDB');

        // Update all users without planType to 'free'
        const usersWithoutPlanType = await User.updateMany(
            { planType: { $exists: false } },
            { $set: { planType: 'free' } }
        );
        console.log(`Updated ${usersWithoutPlanType.modifiedCount} users to free plan`);

        // Find users with active plans and update their planType
        const activePlans = await Plan.find({ 
            isPlanActive: true,
            subscriptionStatus: 'active'
        });

        for (const plan of activePlans) {
            await User.findByIdAndUpdate(plan.userId, {
                planType: plan.planType,
                isPlan: true
            });
            console.log(`Updated user ${plan.userId} to ${plan.planType} plan`);
        }

        console.log('Migration completed successfully');
        process.exit(0);
    } catch (error) {
        console.error('Migration failed:', error);
        process.exit(1);
    }
};

// Run migration if this file is executed directly
if (require.main === module) {
    migratePlanType();
}

module.exports = migratePlanType;
