const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

class StripeService {
    constructor() {
        this.stripe = stripe;
    }

    // Create a Stripe customer
    async createCustomer(email, name) {
        try {
            const customer = await this.stripe.customers.create({
                email: email,
                name: name,
            });
            return customer;
        } catch (error) {
            throw new Error(`Failed to create customer: ${error.message}`);
        }
    }

    // Create products and prices (run this once to set up your products)
    async createProducts() {
        try {
            // Create Basic Plan Product
            const basicProduct = await this.stripe.products.create({
                name: 'Basic Plan',
                description: 'Basic subscription plan with enhanced features',
            });

            // Create Basic Plan Price (£9.99/month)
            const basicPrice = await this.stripe.prices.create({
                unit_amount: 999, // £9.99 in pence
                currency: 'gbp',
                recurring: {
                    interval: 'month',
                },
                product: basicProduct.id,
            });

            // Create Premium Plan Product
            const premiumProduct = await this.stripe.products.create({
                name: 'Premium Plan',
                description: 'Premium subscription plan with all features',
            });

            // Create Premium Plan Price (£19.99/month)
            const premiumPrice = await this.stripe.prices.create({
                unit_amount: 1999, // £19.99 in pence
                currency: 'gbp',
                recurring: {
                    interval: 'month',
                },
                product: premiumProduct.id,
            });

            return {
                basic: {
                    product: basicProduct,
                    price: basicPrice
                },
                premium: {
                    product: premiumProduct,
                    price: premiumPrice
                }
            };
        } catch (error) {
            throw new Error(`Failed to create products: ${error.message}`);
        }
    }

    // Create a subscription
    async createSubscription(customerId, priceId) {
        try {
            const subscription = await this.stripe.subscriptions.create({
                customer: customerId,
                items: [{
                    price: priceId,
                }],
                payment_behavior: 'default_incomplete',
                payment_settings: { save_default_payment_method: 'on_subscription' },
                expand: ['latest_invoice.payment_intent'],
            });
            return subscription;
        } catch (error) {
            throw new Error(`Failed to create subscription: ${error.message}`);
        }
    }

    // Cancel a subscription
    async cancelSubscription(subscriptionId) {
        try {
            const subscription = await this.stripe.subscriptions.update(subscriptionId, {
                cancel_at_period_end: true,
            });
            return subscription;
        } catch (error) {
            throw new Error(`Failed to cancel subscription: ${error.message}`);
        }
    }

    // Immediately cancel a subscription
    async cancelSubscriptionImmediately(subscriptionId) {
        try {
            const subscription = await this.stripe.subscriptions.cancel(subscriptionId);
            return subscription;
        } catch (error) {
            throw new Error(`Failed to cancel subscription immediately: ${error.message}`);
        }
    }

    // Retrieve a subscription
    async getSubscription(subscriptionId) {
        try {
            const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
                expand: ['items.data.price']
              });
            //   console.log("this in new one i am getting from stripe"+JSON.stringify(subscription, null, 2));
            return subscription;
        } catch (error) {
            throw new Error(`Failed to retrieve subscription: ${error.message}`);
        }
    }

    // Retrieve a customer
    async getCustomer(customerId) {
        try {
            const customer = await this.stripe.customers.retrieve(customerId);
            return customer;
        } catch (error) {
            throw new Error(`Failed to retrieve customer: ${error.message}`);
        }
    }

    // Create a checkout session for subscription
    async createCheckoutSession(customerId, priceId, successUrl, cancelUrl) {
        try {
            const session = await this.stripe.checkout.sessions.create({
                customer: customerId,
                payment_method_types: ['card'],
                line_items: [{
                    price: priceId,
                    quantity: 1,
                }],
                mode: 'subscription',
                success_url: successUrl,
                cancel_url: cancelUrl,
            });
            return session;
        } catch (error) {
            throw new Error(`Failed to create checkout session: ${error.message}`);
        }
    }

    // Verify webhook signature
    verifyWebhookSignature(payload, signature) {
        try {
            const event = this.stripe.webhooks.constructEvent(
                payload,
                signature,
                process.env.STRIPE_WEBHOOK_SECRET
            );
            return event;
        } catch (error) {
            throw new Error(`Webhook signature verification failed: ${error.message}`);
        }
    }

    // Get all prices
    async getPrices() {
        try {
            const prices = await this.stripe.prices.list({
                active: true,
                expand: ['data.product'],
            });
            return prices;
        } catch (error) {
            throw new Error(`Failed to retrieve prices: ${error.message}`);
        }
    }
}

module.exports = new StripeService();
