const mongoose = require('mongoose');

const planSchema = new mongoose.Schema({
   userId:{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
   },

    planType: {
        type: String,
        enum: ['free', 'basic', 'premium'],
        default: 'free'
    },

    // Pending plan type (for checkout sessions)
    pendingPlanType: {
        type: String,
        enum: ['free', 'basic', 'premium'],
        default: null
    },
    planStartDate: {
        type: Date,
        default: null
    },

    planExpires: {
        type: Date,
        default: null
    },

    planAmount: {
        type: Number,
        default: 0
    },

    isPlanActive: {
        type: Boolean,
        default: false
    },

    // Stripe-specific fields
    stripeCustomerId: {
        type: String,
        default: null
    },

    stripeSubscriptionId: {
        type: String,
        default: null
    },

    stripePriceId: {
        type: String,
        default: null
    },

    subscriptionStatus: {
        type: String,
        enum: ['active', 'canceled', 'incomplete', 'incomplete_expired', 'past_due', 'trialing', 'unpaid'],
        default: "incomplete"
    },

    currentPeriodStart: {
        type: Date,
        default: null
    },

    currentPeriodEnd: {
        type: Date,
        default: null
    }

}, {
    timestamps: true
});

module.exports = mongoose.model('Plan', planSchema);
