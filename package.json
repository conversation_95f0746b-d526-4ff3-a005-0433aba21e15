{"name": "kintess_v2_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^8.1.5", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-async-handler": "^1.2.0", "fs": "^0.0.1-security", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.1", "mongoose": "^8.16.0", "multer": "^2.0.1", "nodemon": "^3.1.10", "openai": "^5.6.0", "pdf-parse": "^1.1.1", "randomstring": "^1.3.1", "stripe": "^18.3.0"}}