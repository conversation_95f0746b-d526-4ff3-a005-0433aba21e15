const express = require('express');
const router = express.Router();
const {
    createSubscription,
    cancelSubscription,
    getSubscriptionStatus,
    handleWebhook,
    changePlanType
} = require('../controllers/subscriptionController');
const verifyToken = require('../middlewere/verifyToken');

// Protected routes (require authentication)
router.post('/create', verifyToken, createSubscription);
router.post('/cancel', verifyToken, cancelSubscription);
router.get('/status', verifyToken, getSubscriptionStatus);
router.post('/change-plan', verifyToken, changePlanType);

// Webhook route (no authentication required, but signature verification is done in controller)
// Note: Raw body parser is already applied in main app for this specific route
router.post('/webhook', handleWebhook);

module.exports = router;
